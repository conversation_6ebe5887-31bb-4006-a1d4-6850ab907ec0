import requestConfig from "@/uni_modules/kantboot/libs/request/request.config";
import routerConfig from "@/uni_modules/kantboot/libs/router/router.config";
import fileConfig from "@/uni_modules/kantboot/libs/file/file.config";
import payConfig from "@/uni_modules/kantboot/libs/pay/pay.config";

let result = {};

// 项目编码
result.projectCode = "makeFriends";

// 微信小程序二维码URL配置
result.wxUrl = {
    makeFriends: '',
};

result.initMakeFriendsConfig = ()=>{
    // const url = 'https://jiaoyou.changchangjiujiu.top/make-friends-admin';
    // const wsUrl = 'wss://jiaoyou.changchangjiujiu.top/websocket';
    // const fileUrl = 'https://file-static.kantboot.com';
    // // 更新微信小程序二维码URL
    // result.wxUrl.makeFriends = 'https://jiaoyou.changchangjiujiu.top/addInvitationGroupId/?invitationGroupId=';
    const url = 'http://*************:10013';
    const wsUrl = 'ws://*************:10013';
    const fileUrl = 'https://file-static.kantboot.com';
    // 更新微信小程序二维码URL
    result.wxUrl.makeFriends = 'http://*************:8081/app-web/?invitationGroupId=';

    requestConfig.rootAddress = `${url}`;
    requestConfig.fileAddress = `${url}/functional-file-web/file`;
    requestConfig.fileUploadAddress = `${url}/functional-file-web/file/upload`;
    requestConfig.websocketAddress = `${wsUrl}/socket-websocket-web/socket`;
    fileConfig.staticFileAddress= fileUrl;
    fileConfig.visitFileAddress= `${url}/functional-file-web/file/visit`;
    fileConfig.fileUploadAddress= `${url}/functional-file-web/file/upload`;
    routerConfig.indexPath = "/pages/project-make-friends-pages/body/body";
}

result.initMeetConfig = ()=>{
	requestConfig.rootAddress = `https://back-end.meetov.com`;
    requestConfig.fileAddress = `https://back-end.meetov.com/functional-file-web/file`;
    requestConfig.fileUploadAddress = `https://back-end.meetov.com/functional-file-web/file/upload`;
    requestConfig.websocketAddress = `wss://back-end.meetov.com/socket-websocket-web/socket`;
    fileConfig.staticFileAddress= 'https://file-static.meetov.com';
    fileConfig.visitFileAddress= `https://back-end.meetov.com/functional-file-web/file/visit`;
    fileConfig.fileUploadAddress= `https://back-end.meetov.com/functional-file-web/file/upload`;
    payConfig.paypalPath = "https://www.meetov.com/paypal.html";
    routerConfig.indexPath = "/pages/project-meet-pages/body/body";
}

result.initAcmConfig = ()=> {
    requestConfig.rootAddress = `https://frps-acm.kantboot.com`;
    requestConfig.fileAddress = `https://frps-acm.kantboot.com/functional-file-web/file`;
    requestConfig.fileUploadAddress = `https://frps-acm.kantboot.com/functional-file-web/file/upload`;
    requestConfig.websocketAddress = `wss://frps-acm.kantboot.com/socket-websocket-web/socket`;
    fileConfig.staticFileAddress= 'https://file-static.kantboot.com';
    fileConfig.visitFileAddress= `https://frps-acm.kantboot.com/functional-file-web/file/visit`;
    fileConfig.fileUploadAddress= `https://frps-acm.kantboot.com/functional-file-web/file/upload`;
    routerConfig.indexPath = "/pages/project-meet-pages/body/body";
}

export default result;